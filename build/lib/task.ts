/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import fancyLog from 'fancy-log';
import ansiColors from 'ansi-colors';

export interface BaseTask {
	displayName?: string;
	taskName?: string;
	_tasks?: Task[];
}
export interface PromiseTask extends BaseTask {
	(): Promise<void>;
}
export interface StreamTask extends BaseTask {
	(): NodeJS.ReadWriteStream;
}
export interface CallbackTask extends BaseTask {
	(cb?: (err?: any) => void): void;
}

export type Task = PromiseTask | StreamTask | CallbackTask;

function _isPromise(p: Promise<void> | NodeJS.ReadWriteStream): p is Promise<void> {
	if (typeof (<any>p).then === 'function') {
		return true;
	}
	return false;
}

function _renderTime(time: number): string {
	return `${Math.round(time)} ms`;
}

async function _execute(task: Task): Promise<void> {
	const name = task.taskName || task.displayName || `<anonymous>`;
	if (!task._tasks) {
		fancyLog('Starting', ansiColors.cyan(name), '...');
	} else {
		fancyLog('Starting composite task', ansiColors.cyan(name), 'with', ansiColors.yellow(task._tasks.length.toString()), 'subtasks...');
	}
	const startTime = process.hrtime();
	try {
		await _doExecute(task);
	} catch (err) {
		// 记录子任务异常
		fancyLog.error(`任务 ${ansiColors.red(name)} 执行异常:`, err);
		throw err;
	}
	const elapsedArr = process.hrtime(startTime);
	const elapsedNanoseconds = (elapsedArr[0] * 1e9 + elapsedArr[1]);
	if (!task._tasks) {
		fancyLog(`Finished`, ansiColors.cyan(name), 'after', ansiColors.magenta(_renderTime(elapsedNanoseconds / 1e6)));
	} else {
		fancyLog(`Finished composite task`, ansiColors.cyan(name), 'after', ansiColors.magenta(_renderTime(elapsedNanoseconds / 1e6)));
	}
}

async function _doExecute(task: Task): Promise<void> {
	const name = task.taskName || task.displayName || `<anonymous>`;
	return new Promise((resolve, reject) => {
		try {
			if (task.length === 1) {
				// callback 任务
				fancyLog(`执行 callback 任务: ${ansiColors.cyan(name)}`);
				task((err) => {
					if (err) {
						fancyLog.error(`任务 ${ansiColors.red(name)} callback 异常:`, err);
						return reject(err);
					}
					fancyLog(`Callback 任务 ${ansiColors.cyan(name)} 完成`);
					resolve();
				});
				return;
			}

			fancyLog(`执行任务函数: ${ansiColors.cyan(name)}`);
			const taskResult = task();

			if (typeof taskResult === 'undefined') {
				// 同步任务
				fancyLog(`同步任务 ${ansiColors.cyan(name)} 完成`);
				resolve();
				return;
			}

			if (_isPromise(taskResult)) {
				// Promise 任务
				fancyLog(`等待 Promise 任务: ${ansiColors.cyan(name)}`);
				taskResult.then(() => {
					fancyLog(`Promise 任务 ${ansiColors.cyan(name)} 完成`);
					resolve();
				}, (err) => {
					fancyLog.error(`任务 ${ansiColors.red(name)} Promise 异常:`, err);
					reject(err);
				});
				return;
			}

			// Stream 任务
			fancyLog(`等待 Stream 任务: ${ansiColors.cyan(name)}`);

			// 添加超时检测 - 针对不同任务设置不同的警告时间
			let timeoutDuration = 10 * 60 * 1000; // 默认 5 分钟
			if (name.includes('bundle-non-native-extensions') || name.includes('webpack')) {
				timeoutDuration = 20 * 60 * 1000; // webpack 任务 10 分钟
			}

			const timeout = setTimeout(() => {
				fancyLog.warn(`任务 ${ansiColors.yellow(name)} 可能未正确发出 'end' 事件，已等待超过 ${timeoutDuration / 60000} 分钟`);
				fancyLog.info(`如果是 webpack 打包任务，这可能是正常的，请耐心等待...`);
			}, timeoutDuration);

			taskResult.on('end', () => {
				clearTimeout(timeout);
				fancyLog(`Stream 任务 ${ansiColors.cyan(name)} 完成`);
				resolve();
			});

			taskResult.on('error', err => {
				clearTimeout(timeout);
				fancyLog.error(`任务 ${ansiColors.red(name)} Stream 异常:`, err);
				reject(err);
			});

			// 监听 finish 事件作为备选
			taskResult.on('finish', () => {
				clearTimeout(timeout);
				fancyLog(`Stream 任务 ${ansiColors.cyan(name)} 通过 finish 事件完成`);
				resolve();
			});

		} catch (err) {
			// 捕获同步抛出的异常
			fancyLog.error(`任务 ${ansiColors.red(name)} 执行时抛出异常:`, err);
			reject(err);
		}
	});
}

export function series(...tasks: Task[]): PromiseTask {
	const result = async () => {
		fancyLog(`开始执行 series 任务序列，共 ${ansiColors.yellow(tasks.length.toString())} 个子任务`);
		for (let i = 0; i < tasks.length; i++) {
			const taskName = tasks[i].taskName || tasks[i].displayName || `<anonymous-${i}>`;
			fancyLog(`执行第 ${ansiColors.yellow((i + 1).toString())}/${ansiColors.yellow(tasks.length.toString())} 个子任务: ${ansiColors.cyan(taskName)}`);
			await _execute(tasks[i]);
			fancyLog(`完成第 ${ansiColors.yellow((i + 1).toString())}/${ansiColors.yellow(tasks.length.toString())} 个子任务: ${ansiColors.cyan(taskName)}`);
		}
		fancyLog(`series 任务序列全部完成`);
	};
	result._tasks = tasks;
	return result;
}

export function parallel(...tasks: Task[]): PromiseTask {
	const result = async () => {
		await Promise.all(tasks.map(t => _execute(t)));
	};
	result._tasks = tasks;
	return result;
}

export function define(name: string, task: Task): Task {
	if (task._tasks) {
		// This is a composite task
		const lastTask = task._tasks[task._tasks.length - 1];

		if (lastTask._tasks || lastTask.taskName) {
			// This is a composite task without a real task function
			// => generate a fake task function
			return define(name, series(task, () => Promise.resolve()));
		}

		lastTask.taskName = name;
		task.displayName = name;
		return task;
	}

	// This is a simple task
	task.taskName = name;
	task.displayName = name;
	return task;
}
