/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

const es = require('event-stream');
const fancyLog = require('fancy-log');
const ansiColors = require('ansi-colors');

/**
 * 为Stream添加超时处理和多事件监听
 * @param {NodeJS.ReadWriteStream} stream - 要处理的流
 * @param {string} taskName - 任务名称，用于日志
 * @param {number} timeoutMs - 超时时间（毫秒），默认10分钟
 * @returns {NodeJS.ReadWriteStream} 包装后的流
 */
function withTimeout(stream, taskName, timeoutMs = 10 * 60 * 1000) {
    const output = es.through();
    let isCompleted = false;
    let timeout;

    const complete = (eventType) => {
        if (!isCompleted) {
            isCompleted = true;
            if (timeout) {
                clearTimeout(timeout);
            }
            fancyLog(`Stream任务 ${ansiColors.cyan(taskName)} 通过 ${eventType} 事件完成`);
            output.end();
        }
    };

    const handleError = (err) => {
        if (!isCompleted) {
            isCompleted = true;
            if (timeout) {
                clearTimeout(timeout);
            }
            fancyLog.error(`Stream任务 ${ansiColors.red(taskName)} 出现错误:`, err);
            output.emit('error', err);
        }
    };

    // 设置超时
    timeout = setTimeout(() => {
        if (!isCompleted) {
            fancyLog.warn(`Stream任务 ${ansiColors.yellow(taskName)} 超时 (${timeoutMs}ms)，强制完成`);
            complete('timeout');
        }
    }, timeoutMs);

    // 监听多个完成事件
    stream.on('end', () => complete('end'));
    stream.on('finish', () => complete('finish'));
    stream.on('close', () => complete('close'));
    stream.on('error', handleError);

    // 传递数据
    stream.on('data', (data) => {
        if (!isCompleted) {
            output.emit('data', data);
        }
    });

    return output;
}

/**
 * 为marketplace扩展下载创建带超时的流
 * @param {Function} streamFactory - 创建流的工厂函数
 * @param {string} taskName - 任务名称
 * @returns {NodeJS.ReadWriteStream} 包装后的流
 */
function createMarketplaceStream(streamFactory, taskName) {
    fancyLog(`开始执行marketplace扩展任务: ${ansiColors.cyan(taskName)}`);
    const startTime = Date.now();
    
    try {
        const stream = streamFactory();
        return withTimeout(stream, taskName, 15 * 60 * 1000) // 15分钟超时
            .on('end', () => {
                const duration = Date.now() - startTime;
                fancyLog(`Marketplace扩展任务 ${ansiColors.green(taskName)} 完成，耗时: ${duration}ms`);
            });
    } catch (err) {
        fancyLog.error(`创建marketplace扩展流时出错 ${ansiColors.red(taskName)}:`, err);
        throw err;
    }
}

module.exports = {
    withTimeout,
    createMarketplaceStream
};
