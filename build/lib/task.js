"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.series = series;
exports.parallel = parallel;
exports.define = define;
const fancy_log_1 = __importDefault(require("fancy-log"));
const ansi_colors_1 = __importDefault(require("ansi-colors"));
function _isPromise(p) {
    if (typeof p.then === 'function') {
        return true;
    }
    return false;
}
function _renderTime(time) {
    return `${Math.round(time)} ms`;
}
async function _execute(task) {
    const name = task.taskName || task.displayName || `<anonymous>`;
    if (!task._tasks) {
        (0, fancy_log_1.default)('Starting', ansi_colors_1.default.cyan(name), '...');
    }
    else {
        (0, fancy_log_1.default)('Starting composite task', ansi_colors_1.default.cyan(name), 'with', ansi_colors_1.default.yellow(task._tasks.length.toString()), 'subtasks...');
    }
    const startTime = process.hrtime();
    try {
        await _doExecute(task);
    }
    catch (err) {
        // 记录子任务异常
        fancy_log_1.default.error(`任务 ${ansi_colors_1.default.red(name)} 执行异常:`, err);
        throw err;
    }
    const elapsedArr = process.hrtime(startTime);
    const elapsedNanoseconds = (elapsedArr[0] * 1e9 + elapsedArr[1]);
    if (!task._tasks) {
        (0, fancy_log_1.default)(`Finished`, ansi_colors_1.default.cyan(name), 'after', ansi_colors_1.default.magenta(_renderTime(elapsedNanoseconds / 1e6)));
    }
    else {
        (0, fancy_log_1.default)(`Finished composite task`, ansi_colors_1.default.cyan(name), 'after', ansi_colors_1.default.magenta(_renderTime(elapsedNanoseconds / 1e6)));
    }
}
async function _doExecute(task) {
    const name = task.taskName || task.displayName || `<anonymous>`;
    return new Promise((resolve, reject) => {
        try {
            if (task.length === 1) {
                // callback 任务
                (0, fancy_log_1.default)(`执行 callback 任务: ${ansi_colors_1.default.cyan(name)}`);
                task((err) => {
                    if (err) {
                        fancy_log_1.default.error(`任务 ${ansi_colors_1.default.red(name)} callback 异常:`, err);
                        return reject(err);
                    }
                    (0, fancy_log_1.default)(`Callback 任务 ${ansi_colors_1.default.cyan(name)} 完成`);
                    resolve();
                });
                return;
            }
            (0, fancy_log_1.default)(`执行任务函数: ${ansi_colors_1.default.cyan(name)}`);
            const taskResult = task();
            if (typeof taskResult === 'undefined') {
                // 同步任务
                (0, fancy_log_1.default)(`同步任务 ${ansi_colors_1.default.cyan(name)} 完成`);
                resolve();
                return;
            }
            if (_isPromise(taskResult)) {
                // Promise 任务
                (0, fancy_log_1.default)(`等待 Promise 任务: ${ansi_colors_1.default.cyan(name)}`);
                taskResult.then(() => {
                    (0, fancy_log_1.default)(`Promise 任务 ${ansi_colors_1.default.cyan(name)} 完成`);
                    resolve();
                }, (err) => {
                    fancy_log_1.default.error(`任务 ${ansi_colors_1.default.red(name)} Promise 异常:`, err);
                    reject(err);
                });
                return;
            }
            // Stream 任务
            (0, fancy_log_1.default)(`等待 Stream 任务: ${ansi_colors_1.default.cyan(name)}`);
            // 添加超时检测
            const timeout = setTimeout(() => {
                fancy_log_1.default.warn(`任务 ${ansi_colors_1.default.yellow(name)} 可能未正确发出 'end' 事件，已等待超过 20 分钟`);
            }, 20 * 60 * 1000); // 20分钟超时警告
            taskResult.on('end', () => {
                clearTimeout(timeout);
                (0, fancy_log_1.default)(`Stream 任务 ${ansi_colors_1.default.cyan(name)} 完成`);
                resolve();
            });
            taskResult.on('error', err => {
                clearTimeout(timeout);
                fancy_log_1.default.error(`任务 ${ansi_colors_1.default.red(name)} Stream 异常:`, err);
                reject(err);
            });
            // 监听 finish 事件作为备选
            taskResult.on('finish', () => {
                clearTimeout(timeout);
                (0, fancy_log_1.default)(`Stream 任务 ${ansi_colors_1.default.cyan(name)} 通过 finish 事件完成`);
                resolve();
            });
        }
        catch (err) {
            // 捕获同步抛出的异常
            fancy_log_1.default.error(`任务 ${ansi_colors_1.default.red(name)} 执行时抛出异常:`, err);
            reject(err);
        }
    });
}
function series(...tasks) {
    const result = async () => {
        (0, fancy_log_1.default)(`开始执行 series 任务序列，共 ${ansi_colors_1.default.yellow(tasks.length.toString())} 个子任务`);
        for (let i = 0; i < tasks.length; i++) {
            const taskName = tasks[i].taskName || tasks[i].displayName || `<anonymous-${i}>`;
            (0, fancy_log_1.default)(`执行第 ${ansi_colors_1.default.yellow((i + 1).toString())}/${ansi_colors_1.default.yellow(tasks.length.toString())} 个子任务: ${ansi_colors_1.default.cyan(taskName)}`);
            await _execute(tasks[i]);
            (0, fancy_log_1.default)(`完成第 ${ansi_colors_1.default.yellow((i + 1).toString())}/${ansi_colors_1.default.yellow(tasks.length.toString())} 个子任务: ${ansi_colors_1.default.cyan(taskName)}`);
        }
        (0, fancy_log_1.default)(`series 任务序列全部完成`);
    };
    result._tasks = tasks;
    return result;
}
function parallel(...tasks) {
    const result = async () => {
        await Promise.all(tasks.map(t => _execute(t)));
    };
    result._tasks = tasks;
    return result;
}
function define(name, task) {
    if (task._tasks) {
        // This is a composite task
        const lastTask = task._tasks[task._tasks.length - 1];
        if (lastTask._tasks || lastTask.taskName) {
            // This is a composite task without a real task function
            // => generate a fake task function
            return define(name, series(task, () => Promise.resolve()));
        }
        lastTask.taskName = name;
        task.displayName = name;
        return task;
    }
    // This is a simple task
    task.taskName = name;
    task.displayName = name;
    return task;
}
//# sourceMappingURL=task.js.map
