#!/usr/bin/env node

/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/**
 * 测试marketplace扩展构建的脚本
 * 用于验证超时修复是否有效
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const BUILD_TIMEOUT = 20 * 60 * 1000; // 20分钟总超时
const LOG_FILE = path.join(__dirname, '..', 'build-test.log');

function log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    console.log(logMessage.trim());
    fs.appendFileSync(LOG_FILE, logMessage);
}

function runBuildTest() {
    log('开始测试marketplace扩展构建...');
    
    // 清理之前的日志
    if (fs.existsSync(LOG_FILE)) {
        fs.unlinkSync(LOG_FILE);
    }
    
    const startTime = Date.now();
    
    // 运行构建命令
    const buildProcess = spawn('npm', ['run', 'gulp', 'bundle-marketplace-extensions-build'], {
        cwd: path.dirname(__dirname),
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, NODE_ENV: 'development' }
    });
    
    let buildOutput = '';
    let buildError = '';
    
    buildProcess.stdout.on('data', (data) => {
        const output = data.toString();
        buildOutput += output;
        log(`STDOUT: ${output.trim()}`);
    });
    
    buildProcess.stderr.on('data', (data) => {
        const error = data.toString();
        buildError += error;
        log(`STDERR: ${error.trim()}`);
    });
    
    // 设置总体超时
    const timeout = setTimeout(() => {
        log('构建超时，终止进程...');
        buildProcess.kill('SIGTERM');
        
        setTimeout(() => {
            if (!buildProcess.killed) {
                log('强制终止进程...');
                buildProcess.kill('SIGKILL');
            }
        }, 5000);
    }, BUILD_TIMEOUT);
    
    buildProcess.on('close', (code) => {
        clearTimeout(timeout);
        const duration = Date.now() - startTime;
        
        log(`构建进程结束，退出码: ${code}, 耗时: ${duration}ms`);
        
        if (code === 0) {
            log('✅ 构建成功完成！');
            console.log('\n=== 构建成功 ===');
            console.log(`耗时: ${Math.round(duration / 1000)}秒`);
        } else {
            log('❌ 构建失败');
            console.log('\n=== 构建失败 ===');
            console.log(`退出码: ${code}`);
            console.log(`耗时: ${Math.round(duration / 1000)}秒`);
            
            if (buildError) {
                console.log('\n错误输出:');
                console.log(buildError);
            }
        }
        
        console.log(`\n详细日志已保存到: ${LOG_FILE}`);
        process.exit(code);
    });
    
    buildProcess.on('error', (err) => {
        clearTimeout(timeout);
        log(`构建进程启动失败: ${err.message}`);
        console.error('构建进程启动失败:', err);
        process.exit(1);
    });
}

// 检查是否在正确的目录
if (!fs.existsSync(path.join(__dirname, '..', 'package.json'))) {
    console.error('错误: 请在VSCode项目根目录运行此脚本');
    process.exit(1);
}

log('=== 开始marketplace扩展构建测试 ===');
runBuildTest();
